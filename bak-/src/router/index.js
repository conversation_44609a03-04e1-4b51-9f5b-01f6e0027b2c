import Vue from 'vue'
import Router from 'vue-router'
import CorpSearch from '@/components/CorpSearch'
import Login from '@/components/Login'
import Content from '@/components/Content'
import SearchCompany from '@/components/SearchCompany'

Vue.use(Router)

export default new Router({
  //mode:'history',
  routes: [
    {
      path: '/',
      redirect: '/login',
    },
    {
      path:'/content',
      name:'CompanySearch',
      component:Content,
      children:[
        {
          path:"corpsearch",
          name:"corpSearch",
          component:SearchCompany,
        },
        {
          path:"homepage",
          name:"homepage",
          component:CorpSearch,
        }
      ],
    },
    {
      path:'/login',
      component:Login
    }

  ]
})
