<template>
  <div>

    <b-container class="mb-2">
      <b-form>
        <b-form-group>
          <label for="text-password">输入需要搜索的企业名:</label>
          <b-input id="text-password" v-model="companys"></b-input>
          <b-form-text id="password-help-block">多个名字逗号分隔，例：百度,阿里</b-form-text>
        </b-form-group>
      </b-form>
      <label for="text-password">导入Excel批量查询:</label>
      <b-form-file
        @change="exportData"
        v-model="file"
        ref="file-input"
        :state="Boolean(file)"
        placeholder="选择导入文件..."
      ></b-form-file>
      <b-form-text class="mb-2" id="password-help-block">Excel文件格式：首列为公司名，无标题</b-form-text>
            <div class="mt-1" >已选择文件: {{ file ? file.name : '' }}</div>
      <div class="mb-2" >准备导入公司: {{ corps.length }}</div>
      <b-button class="mr-2" type="submit" @click="search()" variant="success">提交</b-button>
      <b-button @click="clearFiles" class="mr-2" variant="danger">重置</b-button>

      <div class="mt-3" v-if="show">公司信息: {{ result["公司信息"] }}</div>
      <div class="mt-3" v-if="show">实际插入或更新项目信息: {{ result["实际插入或更新项目信息条数"] }}</div>
      <div class="mt-3" v-if="show">应插入或更新项目信息: {{ result["项目信息应插入或更新条数"] }}</div>
    </b-container>
  </div>
</template>

<script>
import XLSX from "xlsx";
import { Message } from 'iview';
export default {
  data() {
    return {
      show:false,
      file: null,
      companys: "",
      corps: [],
      result :{}
    };
  },
  methods: {
    search(){
        if (this.companys !=""){
          this.Http.post("/crawlweb/crawCompany",{corpName:this.companys}).then(res=>{
            this.result = res.data.msg
            console.log(this.result)
            this.show = true
          })
        }else{
          Message.warning({content:"请输入公司名称",duration:2})
        };
    },
    clearFiles() {
      this.$refs["file-input"].reset();
      this.companys = "";
      this.corps = [];
    },
    exportData(event) {
      var _this = this;
      if (!event.currentTarget.files.length) {
        return;
      }
      const that = this;
      // 拿取文件对象
      var f = event.currentTarget.files[0];
      // 用FileReader来读取
      var reader = new FileReader();
      // 重写FileReader上的readAsBinaryString方法
      FileReader.prototype.readAsBinaryString = function(f) {
        var binary = "";
        var wb; // 读取完成的数据
        var outdata; // 你需要的数据
        var address_of_cell = "A1";
        var workSheet;
        var reader = new FileReader();
        reader.onload = function(e) {
          // 读取成Uint8Array，再转换为Unicode编码（Unicode占两个字节）
          var bytes = new Uint8Array(reader.result);
          var length = bytes.byteLength;
          for (var i = 0; i < length; i++) {
            binary += String.fromCharCode(bytes[i]);
          }
          // 接下来就是xlsx了，具体可看api
          wb = XLSX.read(binary, {
            type: "binary"
          });
          workSheet = wb.Sheets[wb.SheetNames[0]];
          outdata = XLSX.utils.sheet_to_json(
            workSheet,
            { header: 1 },
            { row: "true" }
          );
          if (_this.corps.length > 1) {
            _this.corps = [];
          }
          outdata.forEach((item, i) => {
            console.log(item, i);
            _this.$set(_this.corps,i, item);
          });

          console.log("sheets: = " + JSON.stringify(_this.corps));
          // 自定义方法向父组件传递数据
          console.log("outdata = " + JSON.stringify(outdata));
          that.$emit("getResult", outdata);
        };
        reader.readAsArrayBuffer(f);
      };
      reader.readAsBinaryString(f);
    }
  }
};
</script>