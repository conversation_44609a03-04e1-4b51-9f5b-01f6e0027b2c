<template>
  <div>
    <b-container class="mb-4">
      <b-row>
        <b-col>
          <b-form class="mb-3" inline>
            <label for="input-1">企业名称:</label>
            <b-form-input
              id="input-1"
              class="ml-2 mb-2 mr-sm-2 mb-sm-0"
              v-model="form.corpName"
              required
              placeholder="企业完整全称"
            ></b-form-input>
          </b-form>
        </b-col>
        <b-col>
          <b-form class="mb-3" inline>
            <label for="input-2">交工日期:</label>
            <b-form-input
              id="input-2"
              class="ml-2 mb-2 mr-sm-2 mb-sm-0"
              v-model="form.handDate"
              required
              placeholder="yyyy-mm-dd"
            ></b-form-input>
          </b-form>
        </b-col>
        <b-col>
          <b-form class="mb-3" inline>
            <label for="input-3">完成公里数(m):</label>
            <b-form-input
              id="input-3"
              class="ml-2 mb-2 mr-sm-2 mb-sm-0"
              v-model="form.kilometer"
              required
              placeholder=">=1000 或 <1000..."
            ></b-form-input>
          </b-form>
        </b-col>
      </b-row>
      <b-row>
        <b-col>
          <b-form class="mb-3" inline>
            <label for="input-4">技术等级:</label>
            <b-form-select
              class="ml-2 mb-2 mr-sm-2 mb-sm-0"
              id="input-4"            
              v-model="form.technologyGrade"
              :options="foods"
            >
              <template slot="first">
                <option :value="null" disabled>-- 请选择 --</option>
              </template>
            </b-form-select>
          </b-form>
        </b-col>
        <b-col>
          <b-form class="mb-3" inline>
            <label for="input-4">关键词:</label>
            <b-form-input
              id="input-4"
              class="ml-2 mb-2 mr-sm-2 mb-sm-0"
              v-model="form.keyWord"
              placeholder="桥梁,路基..."
            ></b-form-input>
          </b-form>
        </b-col>
        <b-col>
          <b-form v-if="show" class="mb-3" inline>
            <b-form-radio-group
              v-model="form.keyType"
              :options="options"
              name="radio-inline"
              @change="changeState"
            ></b-form-radio-group>
          </b-form>
        </b-col>
      </b-row>
      <b-row>
        <b-col>
          <b-form @submit="onSubmit" @reset="onReset" v-if="show" class="mb-3" align="right">
            <b-button class="mr-1" type="submit" variant="success">检索</b-button>
            <b-button type="reset" variant="danger">重置</b-button>
          </b-form>
        </b-col>
      </b-row>

      <!-- <label for="text-password">检索结果:</label> -->
      <b-row></b-row>
      <i-table size="large" border :columns="columns12" :data="data6">
        <template slot-scope="{ row, index }" slot="action">
          <Button type="primary" style="margin-right: 5px" @click="showInfo(index)">查看更多</Button>
        </template>
      </i-table>

      <div align="center">
        <Page
          :total="total"
          size="small"
          :page-size-opts="pageSizeOpts"
          :page-size="pageSize"
          @on-change="onChange"
          @on-page-size-change="onPageSizeChange"
          show-elevator
          show-sizer
          :current="pageIndex"
          show-total
        />
      </div>

      <br />
    </b-container>
  </div>
</template>
<script>
import "iview/dist/styles/iview.css";
import expandRow from "./table-expand.vue";
export default {
  components: { expandRow },
  mounted: function() {
    //var _this = this;
    this.Http.get("/spiderweb/queryItems").then(res => {
      console.log(res);
      if (res == undefined){
        return
      }
      this.totalData = res.data.data;
      this.data6 = this.totalData.slice(this.pageIndex - 1, this.pageSize);
      this.total = this.totalData.length;
      console.log(res.data);
    });
  },

  data() {
    return {
      pageIndex: 1,
      pageSize: 10,
      totalData: [],
      isloading: true,
      total: 0,
      pageSizeOpts: [10, 20, 30, 40],
      columns12: [
        {
          type: "expand",
          width: 50,
          render: (h, params) => {
            return h(expandRow, {
              props: {
                row: params.row
              }
            });
          }
        },
        {
          title: "企业名称",
          key: "corpName"
        },
        {
          title: "工程名称",
          key: "projectName"
        },
        {
          title: "技术等级",
          key: "technologyGrade"
        },
        {
          title: "公里数(单位:m)",
          key: "kilometer"
        },
        {
          title: "操作",
          slot: "action",
          width: 150,
          align: "center"
        }
      ],
      data6: [],
      result: "暂无",
      form: {
        corpName: "",
        kilometer: "",
        endDate: "",
        keyWordy: "",
        technologyGrade: null,
        keyType: "and",
        handDate: ""
      },

      options: [
        { text: "条件为且关系", value: "and" },
        { text: "条件为或关系", value: "or" }
      ],
      foods: [
        { text: "高速公路", value: "高速公路" },
        { text: "一级及以上", value: "一级及以上" },
        { text: "二级及以上", value: "二级及以上" },
        { text: "三级及以上", value: "三级及以上" },
      ],
      show: true
      //count: [0, 1, 2]
    };
  },
  methods: {
    onPageSizeChange(value) {
      this.pageSize = value;
      var _start = (this.pageIndex - 1) * this.pageSize;
      var _end = this.pageIndex * this.pageSize;
      this.data6 = this.totalData.slice(_start, _end);
    },
    onChange(value) {
      this.pageIndex = value;
      var _start = (value - 1) * this.pageSize;
      var _end = value * this.pageSize;
      this.data6 = this.totalData.slice(_start, _end);
    },
    showInfo(index) {
      this.$Modal.info({
        title: "User Info",
        content: `Name：${this.data6[index].name}<br>Age：${this.data6[index].age}<br>Address：${this.data6[index].address}`
      });
    },
    handleAdd() {
      if (this.count.length) {
        this.count.push(this.count[this.count.length - 1] + 1);
      } else {
        this.count.push(0);
      }
    },
    handleClose2(event, name) {
      const index = this.count.indexOf(name);
      this.count.splice(index, 1);
    },
    onSubmit(evt) {
      evt.preventDefault();
      var query = this.Qs.stringify(this.form);
      this.Http.get("/spiderweb/queryItems?" + query).then(res => {
        this.pageIndex = 1;
        this.pageSize = 10;
        console.log(res);
        if (res.data.data != null || res.data.data.length !=0) {
          this.totalData = res.data.data;
          this.data6 = this.totalData.slice(this.pageIndex - 1, this.pageSize);
          this.total = this.totalData.length;
        }

        console.log(res.data);
      });
    },
    changeState(checked) {
      console.log("check: ", checked);
    },
    onReset(evt) {
      evt.preventDefault();
      this.form.corpName = "";
      this.form.keyType = "and";
      this.form.handDate = "";
      this.form.keyWord = "";
      this.form.kilometer = "";
      this.form.technologyGrade = null;
      this.form.checked = [];
      this.show = false;
      this.$nextTick(() => {
        this.show = true;
      });
    }
  }
};
</script>