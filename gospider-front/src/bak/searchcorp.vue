<template>
  <div>
    <b-container class="mb-4">
      <b-form @submit="onSubmit" @reset="onReset" v-if="show" class="mb-3">
        <b-form-group
          id="input-group-1"
          label="企业名称:"
          label-for="input-1"
          description="请输入完整公司名称，以方便检索"
        >
          <b-form-input id="input-1" v-model="form.email" required placeholder="企业名称"></b-form-input>
        </b-form-group>

        <b-form-group
          id="input-group-2"
          label="交工日期:"
          label-for="input-2"
          description="在此时间点以后的所有项目会被检索"
        >
          <b-form-input id="input-2" v-model="form.handDate" required placeholder="yyyy-mm-dd"></b-form-input>
        </b-form-group>

        <b-form-group
          id="input-group-2"
          label="至少完成公里数:"
          label-for="input-2"
          description="例：1000；单位：m"
        >
          <b-form-input id="input-2" v-model="form.kilometer" required placeholder="1000"></b-form-input>
        </b-form-group>

        <b-form-group id="input-group-3" label="技术等级:" label-for="input-3">
          <b-form-select id="input-3" v-model="form.food" :options="foods">
            <template slot="first">
              <option :value="null" disabled>-- 请选择 --</option>
            </template>
          </b-form-select>
        </b-form-group>

        <b-form-group
        size="small"
          id="input-group-2"
          label="关键词:"
          label-for="input-2"
          description="输入关键词，多个用英文逗号分隔，例：桥梁,路基,高速路"
        >
          <b-form-radio-group
            v-model="selected"
            :options="options"
            name="radio-inline"
            @change="changeState"
          ></b-form-radio-group>
          <br />
          <b-form-input id="input-2" v-model="form.keywd" required placeholder="桥梁,路基..."></b-form-input>
        </b-form-group>

        <b-button type="submit" variant="success">检索</b-button>
        <b-button type="reset" variant="danger">重置</b-button>
      </b-form>
    <label for="text-password">检索结果:</label>
      <Table size="large" border :columns="columns12" :data="data6">
        <template slot-scope="{ row, index }" slot="action">
          <Button
            type="primary"
            style="margin-right: 5px"
            @click="showInfo(index)"
          >查看更多</Button>
        </template>
      </Table>
    </b-container>
  
  </div>
</template>
<script>
import expandRow from "./utils/table-expand.vue";
export default {
  components: { expandRow },
  data() {
    return {
      columns12: [
        {
          type: "expand",
          width: 50,
          render: (h, params) => {
            return h(expandRow, {
              props: {
                row: params.row
              }
            });
          }
        },
        {
          title: "企业名称",
          key: "corpName"
        },
        {
          title: "工程名称",
          key: "projectName"
        },
        {
          title: "技术等级",
          key: "techLevel"
        },
        {
          title: "项目类型",
          key: "projectType"
        },
        {
          title: "操作",
          slot: "action",
          width: 150,
          align: "center"
        }
      ],
      data6: [
        {
          corpName: "John Brown",
          projectName: 18,
          projectType: "New York No. 1 Lake Park",
          techLevel: "高速公路",
          interest: "badminton",
          birthday: "1991-05-14",
          book: "Steve Jobs",
          movie: "The Prestige",
          music: "I Cry"
        },
        {
          corpName: "John Brown",
          projectName: 18,
          projectType: "New York No. 1 Lake Park",
          techLevel: "高速公路",
          interest: "volleyball",
          birthday: "1989-03-18",
          book: "My Struggle",
          movie: "Roman Holiday",
          music: "My Heart Will Go On"
        },
        {
          corpName: "John Brown",
          projectName: 18,
          projectType: "New York No. 1 Lake Park",
          techLevel: "高速公路",
          interest: "tennis",
          birthday: "1992-01-31",
          book: "Win",
          movie: "Jobs",
          music: "Don’t Cry"
        },
        {
          corpName: "John Brown",
          projectName: 18,
          projectType: "New York No. 1 Lake Park",
          techLevel: "高速公路",
          interest: "snooker",
          birthday: "1988-7-25",
          book: "A Dream in Red Mansions",
          movie: "A Chinese Ghost Story",
          music: "actor"
        }
      ],
      result: "暂无",
      form: {
        email: "",
        name: "",
        food: null
      },
      selected: "and",
      options: [
        { text: "条件为且关系", value: "and" },
        { text: "条件为或关系", value: "or" }
      ],
      foods: [
        { text: "一级公路", value: "一级公路" },
        { text: "二级公路", value: "二级公路" },
        { text: "高速公路", value: "高速公路" }
      ],
      show: true,
      count: [0, 1, 2]
    };
  },
  methods: {
    showInfo(index) {
      this.$Modal.info({
        title: "User Info",
        content: `Name：${this.data6[index].name}<br>Age：${this.data6[index].age}<br>Address：${this.data6[index].address}`
      });
    },
    handleAdd() {
      if (this.count.length) {
        this.count.push(this.count[this.count.length - 1] + 1);
      } else {
        this.count.push(0);
      }
    },
    handleClose2(event, name) {
      const index = this.count.indexOf(name);
      this.count.splice(index, 1);
    },
    onSubmit(evt) {
      evt.preventDefault();
      alert(JSON.stringify(this.form));
    },
    changeState(checked) {
      console.log("check: ", checked);
    },
    onReset(evt) {
      evt.preventDefault();
      this.form.email = "";
      this.form.handDate = "";
      this.form.keywd = "";
      this.form.kilometer = "";
      this.form.food = null;
      this.form.checked = [];
      this.show = false;
      this.$nextTick(() => {
        this.show = true;
      });
    }
  }
};
</script>