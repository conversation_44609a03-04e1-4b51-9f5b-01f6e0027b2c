// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'
//import BootstrapVue from 'bootstrap-vue'
import Http from './http/axios.js';
import 'babel-polyfill'
import qs from 'qs'
import Blob from '@/excel/Blob.js'
import Export2Excel from '@/excel/Export2Excel.js'

//import ElementUI from 'element-ui';
//import 'element-ui/lib/theme-chalk/index.css';
import axios from 'axios'
//import 'bootstrap/dist/css/bootstrap.css'
//import 'bootstrap-vue/dist/bootstrap-vue.css'
import store from './store/index'
import iView from 'iview';
//import 'iview/dist/styles/iview.css';
//import $ from 'jquery'

Vue.config.productionTip = false
Vue.prototype.Http = Http;
Vue.prototype.Qs = qs
//Vue.use(BootstrapVue)
Vue.use(iView)

Vue.prototype.$axios = axios

/* eslint-disable no-new */
new Vue({
  router: router,
  store,
  render: h => h(App),
}).$mount('#app')
