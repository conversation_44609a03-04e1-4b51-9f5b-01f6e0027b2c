<template>
  <div>
    <Card>
      <Row>
        <Form>
          <FormItem label="输入需要搜索的企业名:">
            <i-input id="text-password" v-model="company" placeholder="只限制输入1个公司名称"></i-input>
          </FormItem>
        </Form>
        <Form>
          <FormItem>
            <CheckboxGroup v-model="status">
              <Checkbox label="在建"></Checkbox>
              <Checkbox label="已建"></Checkbox>
            </CheckboxGroup>
          </FormItem>
        </Form>
        <div>被查询公司: {{ corps }}</div>
        <i-button :loading="loadingStatus" type="primary" @click="search()" variant="success">提交</i-button>
        <i-button @click="clearFiles" type="warning" variant="danger">重置</i-button>

        <div v-if="show">公司信息: {{ result["公司信息"] }}</div>
        <div v-if="show">实际插入或更新项目信息: {{ result["实际插入或更新项目信息条数"] }}</div>
        <div v-if="show">应插入或更新项目信息: {{ result["项目信息应插入或更新条数"] }}</div>
      </Row>
    </Card>
  </div>
</template>

<script>
import XLSX from "xlsx";
import { Message } from "iview";
export default {
  data() {
    return {
      show: false,
      status: ["在建", "已建"],
      file: null,
      company: "",
      loadingStatus: false,
      //  corps: company.split(" "),
      result: {}
    };
  },
  computed: {
    corps: function() {
      if (this.company === "") {
        return this.company.replace(/^\s*|\s*$/g, "").split("").length;
      }
      return this.company.replace(/^\s*|\s*$/g, "").split(" ").length;
    }
  },
  methods: {
    BuildGet(onoff) {
      return this.Http.post("/crawlweb/" + onoff);
    },
    search() {
      let onReq;
      let offReq;
      this.loadingStatus = true;
      if (this.company != "") {
        console.log("post qing qiu ");
        for (let key of this.status) {
          if (key == "在建") {
            onReq = this.Http.post("/crawlweb/crawCompany", {
              corpName: this.company
            });
          } else if (key == "已建") {
            offReq = this.Http.post("/crawlweb/crawCompany2", {
              corpName: this.company
            });
          }
        }
        console.log("onReq: ", onReq);
        onReq
          .then(res => {
            this.result = res.data.msg;
            console.log(this.result);
            this.show = true;
          })
          .then(() => {
            this.loadingStatus = false;
          });
        offReq
          .then(res => {
            this.result = res.data.msg;
            console.log(this.result);
            this.show = true;
          })
          .then(() => {
            this.loadingStatus = false;
          });
        this.clearFiles();
        this.loadingStatus = false;
      } else {
        Message.warning({ content: "请输入公司名称", duration: 2 });
        this.loadingStatus = false;
      }
    },
    clearFiles() {
      this.file = null;
      this.company = "";
    }
  }
};
</script>