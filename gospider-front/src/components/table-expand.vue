<style scoped>
.expand-row {
  margin-bottom: 16px;
}
</style>
<template>
  <div>
    <Row class="expand-row">
      <i-Col span="8">
        <span class="expand-key">技术等级:</span>
        <span class="expand-value">{{ row.projectType }}</span>
      </i-Col>
      <i-Col span="8">
        <span class="expand-key">合同金额:</span>
        <span class="expand-value">{{ row.contractPrice }}</span>
      </i-Col>
      <i-Col span="8">
        <span class="expand-key">结算金额:</span>
        <span class="expand-value">{{ row.settlementPrice }}</span>
      </i-Col>
      <i-Col span="8">
        <span class="expand-key">标段名:</span>
        <span class="expand-value">{{ row.segmentName }}</span>
      </i-Col>
            <i-Col span="8">
        <span class="expand-key">开始施工:</span>
        <span class="expand-value">{{ row.beginDate }}</span>
      </i-Col>
      
      <i-Col span="8">
        <span class="expand-key">交接日期:</span>
        <span class="expand-value">{{ row.handDate }}</span>
      </i-Col>
      <i-Col span="8">
        <span class="expand-key">竣工日期:</span>
        <span class="expand-value">{{ row.endDate }}</span>
      </i-Col>

    </Row>
    <Row class="expand-row">
      <i-Col span="8">
        <span class="expand-key">标段开始:</span>
        <span class="expand-value">{{ row.stakeStart }}</span>
      </i-Col>
      <i-Col span="8">
        <span class="expand-key">标段结束:</span>
        <span class="expand-value">{{ row.stakeEnd }}</span>
      </i-Col>
      <i-Col span="20">
        <span class="expand-key">主要业绩:</span>
        <span class="expand-value">{{ row.remark }}</span>
      </i-Col>
    </Row>
  </div>
</template>
<script>
import "iview/dist/styles/iview.css";
export default {
  props: {
    row: Object
  }
};
</script>