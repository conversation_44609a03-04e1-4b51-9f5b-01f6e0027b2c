<template>
  <div>
    <Header>
      <Menu mode="horizontal" theme="dark" active-name="1">
        <!-- <div class="layout-logo"></div> -->
        <div class="layout-nav"></div>
      </Menu>
    </Header>
    <Row justify="center" class-name="mt">
      <i-col span="6" offset="9">
        <Card span="5">
          <h2 class="mb-4">登录</h2>
          <i-form @submit="onSubmit" @reset="onReset" v-if="show">
            <FormItem id="exampleInputGroup1" label="Email address:" label-for="exampleInput1">
              <i-input
                id="exampleInput1"
                type="email"
                v-model="form.email"
                required
                placeholder="Enter email"
              ></i-input>
            </FormItem>
            <FormItem id="exampleInputGroup2" label="Password:" label-for="exampleInput2">
              <i-input
                id="exampleInput2"
                type="password"
                v-model="form.password"
                required
                placeholder="Enter name"
              ></i-input>
            </FormItem>

            <FormItem id="exampleGroup4">
              <CheckboxGroup v-model="form.checked" id="exampleChecks">
                <Checkbox value="remember">Remember me</Checkbox>
              </CheckboxGroup>
            </FormItem>
            <div class="d-flex justify-content-between">
              <div>
                <Button type="success" variant="primary" @click="redirect">提交</Button>&nbsp;
                <Button type="error" variant="danger">重置</Button>
              </div>
              <div>请直接点提交</div>
            </div>
          </i-form>
        </Card>
      </i-col>
    </Row>
  </div>
</template>


<script>
export default {
  data() {
    return {
      items: [
        {
          isActive: true,
          age: 40,
          first_name: "Dickerson",
          last_name: "Macdonald"
        },
        { isActive: false, age: 21, first_name: "Larsen", last_name: "Shaw" },
        { isActive: false, age: 89, first_name: "Geneva", last_name: "Wilson" },
        { isActive: true, age: 38, first_name: "Jami", last_name: "Carney" }
      ],
      showDismissibleAlert: false,
      form: {
        email: "",
        password: "",
        food: null,
        checked: []
      },
      foods: [
        { text: "Select One", value: null },
        "Carrots",
        "Beans",
        "Tomatoes",
        "Corn"
      ],
      show: true
    };
  },
  methods: {
    handleClick() {
      this.showDismissibleAlert = true;
    },
    redirect(evt) {
      evt.preventDefault();
      this.$router.push("/content/corpsearch");
    },
    onReset(evt) {
      evt.preventDefault();
      /* Reset our form values */
      this.form.email = "";
      this.form.password = "";
      this.form.food = null;
      this.form.checked = [];
      /* Trick to reset/clear native browser form validation state */
      this.show = false;
      this.$nextTick(() => {
        this.show = true;
      });
    }
  }
};
</script>


<style >
.mt {
  margin-top: 8%;
}
</style>
