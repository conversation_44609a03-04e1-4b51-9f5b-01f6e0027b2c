<template>
  <div>
    <Card>
      <div>
        <Row>
          <Form>
            <FormItem label="输入需要搜索的企业名:">
              <i-input id="text-password" v-model="companys" placeholder="确保用英文逗号分隔，中间无空格..."></i-input>
              <!-- <b-form-text id="password-help-block">多个名字逗号分隔，例：百度,阿里</b-form-text> -->
            </FormItem>
          </Form>
          <Form>
            <FormItem label="导入Excel批量查询:">
              <Upload
                ref="file-input"
                :before-upload="handleUpload"
                :show-upload-list="false"
                :on-success="uploadSuccess2"
                :on-error="uploadError"
                :format="['xlsx']"
                :max-size="2048"
                :on-format-error="handleFormatError"
                :data="{noValidateType:0}"
                accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                name="upload"
                action="/api/achievement/add_Excel_Achieve"
              >
                <Button icon="ios-cloud-upload-outline">批量导入</Button>
              </Upload>
            </FormItem>
          </Form>
          <div>被查询公司: {{ corps.length }}</div>
          <i-button :loading="loadingStatus" type="primary" @click="search()" variant="success">提交</i-button>
          <i-button @click="clearFiles" type="warning" variant="danger">重置</i-button>

          <div v-if="show">公司信息: {{ result["公司信息"] }}</div>
          <div v-if="show">实际插入或更新项目信息: {{ result["实际插入或更新项目信息条数"] }}</div>
          <div v-if="show">应插入或更新项目信息: {{ result["项目信息应插入或更新条数"] }}</div>
        </Row>
      </div>
    </Card>
  </div>
</template>

<script>
import XLSX from "xlsx";
import { Message } from "iview";
export default {
  data() {
    return {
      show: false,
      file: null,
      companys: "",
      loadingStatus: false,
      corps: [],
      result: {}
    };
  },
  methods: {
    handleUpload(file) {
      this.file = file;
      var _this = this;
      if (!event.currentTarget.files.length) {
        return;
      }
      const that = this;
      // 拿取文件对象
      //var f = event.currentTarget.files[0];
      var f = file;
      // 用FileReader来读取
      var reader = new FileReader();
      // 重写FileReader上的readAsBinaryString方法
      FileReader.prototype.readAsBinaryString = function(f) {
        var binary = "";
        var wb; // 读取完成的数据
        var outdata; // 你需要的数据
        var address_of_cell = "A1";
        var workSheet;
        var reader = new FileReader();
        reader.onload = function(e) {
          // 读取成Uint8Array，再转换为Unicode编码（Unicode占两个字节）
          var bytes = new Uint8Array(reader.result);
          var length = bytes.byteLength;
          for (var i = 0; i < length; i++) {
            binary += String.fromCharCode(bytes[i]);
          }
          // 接下来就是xlsx了，具体可看api
          wb = XLSX.read(binary, {
            type: "binary"
          });
          workSheet = wb.Sheets[wb.SheetNames[0]];
          outdata = XLSX.utils.sheet_to_json(
            workSheet,
            { header: 1 },
            { row: "true" }
          );
          if (_this.corps.length > 1) {
            _this.corps = [];
          }
          outdata.forEach((item, i) => {
            // let v = item[0]
            // console.log(item.length, i);
            if (item.length != 0) {
              //   console.log(item)
              // _this.$set(_this.corps, i, item[0]);
              _this.corps.push(item[0]);
            }
          });
          _this.companys = _this.corps.join(",");
          // console.log("sheets: = " + JSON.stringify(_this.corps));
          // 自定义方法向父组件传递数据
          //   console.log("outdata = " + JSON.stringify(outdata));
          that.$emit("getResult", outdata);
        };
        reader.readAsArrayBuffer(f);
      };
      reader.readAsBinaryString(f);

      return false;
    },

    uploadSuccess2(res, file) {
      //上传成功
      this.$Message.info(res.msg);
      if (res.code == 200) {
        this.excel_name = res.info.originalName;
      }
    },
    uploadError(error, file) {
      //上传失败
      this.$Message.info(error);
    },

    handleFormatError(file, filelist) {
      this.$Message.info("文件格式不正确,请上传excel格式文件");
    },
    uploadError(error, file) {
      this.$Message.info(error);
    },
    upload() {
      this.loadingStatus = true;
      setTimeout(() => {
        this.file = null;
        this.loadingStatus = false;
        this.$Message.success("上传成功");
      }, 1500);
    },
    search() {
      this.loadingStatus = true;
      if (this.companys != "") {
        // console.log("post qing qiu ");
        this.Http.post("/crawlweb/multiCrawl", {
          corpNames: this.companys
        }).then(res => {
          this.result = res.data.msg;
          //    console.log(this.result);
          this.show = true;
        });
        this.clearFiles();
      } else {
        Message.warning({ content: "请输入公司名称", duration: 2 });
      }
      this.loadingStatus = false;
    },
    clearFiles() {
      this.$refs["file-input"].clearFiles();
      this.file = null;
      this.companys = "";
      this.corps = [];
    }
  }
};
</script>