<template>
  <div class="layout">
    <Layout>
      <Header>
        <!-- <Menu mode="horizontal" theme="dark" active-name="1">
          <div class="layout-nav">
            <MenuItem name="1" to="/content/corpsearch">
              <Icon type="ios-navigate"></Icon>检索信息
            </MenuItem>
            <MenuItem name="2" to="/content/homepage">
              <Icon type="ios-keypad"></Icon>录入公司
            </MenuItem>
            <MenuItem name="3">
              <Icon type="ios-analytics"></Icon>已录入公司
            </MenuItem>
          </div>
        </Menu> -->
      </Header>
      <Layout>
        <Sider
          ref="side1"
          :style="{background: '#fff'}"
          hide-trigger
          collapsible
          :collapsed-width="78"
          v-model="isCollapsed"
        >
          <Menu active-name="1-2" theme="light" width="auto" :class="menuitemClasses">
            <MenuItem name="1-1" to="/content/corpsearch">
              <Icon type="ios-navigate"></Icon>
              <span>检索信息</span>
            </MenuItem>
            <MenuItem name="1-2" to="/content/homepage">
              <Icon type="ios-search"></Icon>
              <span>搜索公司</span>
            </MenuItem>
            <MenuItem name="1-3">
              <Icon type="ios-keypad"></Icon>
              <span>已录入</span>
            </MenuItem>
          </Menu>
        </Sider>
        <Layout :style="{padding: '0 0px 24px'}">
          <Content :style="{padding: '0 50px'}">
            <Breadcrumb :style="{margin: '20px 0'}">
              <Icon
                @click.native="collapsedSider"
                :class="rotateIcon"
                type="md-menu"
                size="24"
                :style="{padding: '0 10px 0 0'}"
              ></Icon>
              <BreadcrumbItem to="/content/homepage">首页</BreadcrumbItem>
              <BreadcrumbItem>检索</BreadcrumbItem>
            </Breadcrumb>

            <router-view></router-view>
          </Content>
          <!-- <Footer class="layout-footer-center">2011-2016 &copy; TalkingData</Footer> -->
        </Layout>
      </Layout>
    </Layout>
  </div>
</template>
<script>
export default {};
</script>

<style scoped>
.layout {
  border: 1px solid #d7dde4;
  background: #f5f7f9;
  position: relative;
  border-radius: 4px;
  overflow: hidden;
}
.layout-logo {
  width: 100px;
  height: 30px;
  background: #5b6270;
  border-radius: 3px;
  float: left;
  position: relative;
  top: 15px;
  left: 20px;
}
.menu-item span {
  display: inline-block;
  overflow: hidden;
  width: 69px;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: bottom;
  transition: width 0.2s ease 0.2s;
}
.rotate-icon {
  transform: rotate(-90deg);
}
.layout-nav {
  width: 420px;
  margin: 0 auto;
  margin-right: 15px;
}
.menu-item i {
  transform: translateX(0px);
  transition: font-size 0.2s ease, transform 0.2s ease;
  vertical-align: middle;
  font-size: 16px;
}
.collapsed-menu span {
  width: 0px;
  transition: width 0.2s ease;
}
.collapsed-menu i {
  transform: translateX(5px);
  transition: font-size 0.2s ease 0.2s, transform 0.2s ease 0.2s;
  vertical-align: middle;
  font-size: 22px;
}
.layout-footer-center {
  text-align: center;
}
</style>

<script>
export default {
  data() {
    return {
      isCollapsed: false
    };
  },
  computed: {
    menuitemClasses: function() {
      return ["menu-item", this.isCollapsed ? "collapsed-menu" : ""];
    },
    rotateIcon() {
      return ["menu-icon", this.isCollapsed ? "rotate-icon" : ""];
    }
  },
  methods: {
    collapsedSider() {
      this.$refs.side1.toggleCollapse();
    }
  }
};
</script>