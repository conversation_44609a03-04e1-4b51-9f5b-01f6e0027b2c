<template>
  <div style="min-height: 200px;">
    <Card>
      <Row>
        <i-col span="8">
          <Form inline>
            <FormItem label="企业名称:" :label-width="80">
              <i-input
                prefix="ios-contacts"
                id="input-1"
                v-model="form.corpName"
                clearable
                required
                placeholder="企业完整全称"
              ></i-input>
            </FormItem>
          </Form>
        </i-col>

        <i-col span="8">
          <Form class="mb-3" inline>
            <FormItem label="交工起止:" :label-width="80">
              <DatePicker type="daterange" placeholder="yyyy-mm-dd" v-model="form.handDate"></DatePicker>
            </FormItem>
          </Form>
        </i-col>
        <i-col span="8">
          <Form inline>
            <FormItem label="完成里程数(单位：km):" :label-width="80">
              <InputNumber
                clearable
                id="input-3"
                prefix="ios-checkbox"
                style="width: 45%"
                v-model="form.kilometerSmall"
                placeholder="最低值"
              ></InputNumber>
              <!-- </FormItem>
              <FormItem>-->
              <InputNumber
                clearable
                id="input-3"
                prefix="ios-checkbox"
                style="width: 45%"
                v-model="form.kilometerBig"
                placeholder="最高值"
              ></InputNumber>
            </FormItem>
          </Form>
        </i-col>
        <!-- </Row>

        <Row>-->
        <i-col span="8">
          <Form inline>
            <FormItem
              label="修建状态:"
              :label-width="80"
              id="input-15"
              v-model="form.constructStatus"
              :options="constrStatus"
            >
              <Select
                v-model="form.constructStatus"
                clearable
                placeholder="--请选择--"
                prefix="ios-list-box"
                style="width:170%"
              >
                <Option
                  v-for="item in constrStatus"
                  :value="item.value"
                  :key="item.value"
                >{{ item.text }}</Option>
              </Select>
            </FormItem>
          </Form>
        </i-col>

        <i-col span="8">
          <Form class="mb-3" inline>
            <FormItem label="竣工起止:" :label-width="80">
              <DatePicker
                type="daterange"
                placeholder="yyyy-mm-dd"
                split-panels
                v-model="form.endDate"
              ></DatePicker>
            </FormItem>
          </Form>
        </i-col>

        <i-col span="8">
          <Form class="mb-3" inline>
            <FormItem label="合同金额(单位：1000):" :label-width="80">
              <InputNumber
                prefix="logo-yen"
                id="input-1"
                v-model="form.priceSmall"
                clearable
                required
                style="width: 45%"
                placeholder="最低值"
              ></InputNumber>
              <InputNumber
                prefix="logo-yen"
                id="input-1"
                style="width: 45%"
                v-model="form.priceBig"
                clearable
                required
                placeholder="最高值"
              ></InputNumber>
            </FormItem>
          </Form>
        </i-col>
        <i-col span="24">
          <Form inline>
            <FormItem label="关键词:" :label-width="80">
              <i-input
                clearable
                id="input-4"
                prefix="ios-key"
                v-model="keyWordsStr"
                placeholder="(空格分隔)桥梁 路基..."
              ></i-input>
            </FormItem>
            <FormItem>
              <Tag
                v-for="item in this.keywordsArray"
                :key="item"
                :name="item"
                closable
                color="success"
                @on-close="handleClose2"
              >{{ item }}</Tag>
            </FormItem>
            <FormItem>
              <RadioGroup v-model="form.keyType" v-if="keywordsArray.length>0">
                <Radio label="and"></Radio>
                <Radio label="or"></Radio>
              </RadioGroup>
            </FormItem>
          </Form>
        </i-col>
        <!-- </Row>

        <Row>-->
        <i-col span="24">
          <!-- <Form inline @submit="onSubmit" v-if="show" align="right"> -->
          <Form inline align="right" v-if="show">
            <FormItem>
              <Button class="mr-1" type="success" @click="onSubmit" variant="success">检索</Button>
              <Button type="error" variant="danger" @click="onReset">重置</Button>
            </FormItem>
          </Form>
        </i-col>
      </Row>
    </Card>
    <Row>
      <i-col span="2">
        <div style="margin:12px">
          行号
          <i-switch v-model="showIndex" style="margin-right: 5px"></i-switch>
          <!-- <i-switch v-model="showCheckbox" style="margin-right: 5px"></i-switch> -->
        </div>
      </i-col>
    </Row>
    <Row>
      <i-col>
        <i-table
          size="default"
          :loading="isloading"
          :stripe="true"
          border
          ref="expandTable"
          :columns="newcolumns"
          :data="data6"
        >
          <template slot-scope="{ row, index }" slot="action">
            <Button type="primary" style="margin-right: 5px" @click="showInfo(index)">查看更多</Button>
          </template>
        </i-table>
      </i-col>
    </Row>
    <Row>
      <i-col>
        <div style="margin-top:2px">
          <Button type="primary" @click="closeExpand()">
            <Icon type="ios-close"></Icon>收起扩展行
          </Button>
          <Button type="primary" @click="downloadSelected()">
            <Icon type="ios-download-outline"></Icon>下载当前页
          </Button>
        </div>
      </i-col>
    </Row>
    <div align="center">
      <Page
        :total="total"
        size="small"
        :page-size-opts="pageSizeOpts"
        :page-size="pageSize"
        @on-change="onChange"
        @on-page-size-change="onPageSizeChange"
        show-elevator
        show-sizer
        :current="pageIndex"
        show-total
      />
    </div>

    <br />
    <!-- </Row> -->
  </div>
</template>
<script>
import expandRow from "./table-expand.vue";
export default {
  components: { expandRow },
  mounted: function() {
    this.isloading = true;
    this.Http.get("/spiderweb/queryItems")
      .then(res => {
        console.log(res);
        if (res == undefined) {
          return;
        }
        this.totalData = res.data.data;
        this.data6 = this.totalData.slice(this.pageIndex - 1, this.pageSize);
        this.total = this.totalData.length;
        this.isloading = false;
        console.log(res.data);
      })
      .then(() => {
        this.isloading = false;
      });
  },
  computed: {
    getFilters() {
      let provinces = [];
      let filter = [];
      for (var key in this.data6) {
        let element = this.data6[key]["province"];
        if (filter.indexOf(element) == -1) {
          provinces.push({
            label: element,
            value: element
          });
        }
        filter.push(element);
      }
      return provinces;
    },
    newcolumns() {
      let mycolumns = [];
      if (this.showIndex) {
        mycolumns.push({
          type: "index",
          width: 60,
          align: "center"
        });
      }
      mycolumns.push({
        type: "expand",
        width: 50,
        render: (h, params) => {
          return h(expandRow, {
            props: {
              row: params.row
            }
          });
        }
      });
      mycolumns.push({
        title: "企业名称",
        key: "corpName",
        sortable: true
      });

      mycolumns.push({
        title: "工程名称",
        key: "projectName"
      });
      mycolumns.push({
        title: "技术等级",
        key: "technologyGrade",
        filters: [
          {
            label: "高速公路",
            value: ["高速公路"]
          },
          {
            label: "一级及以上",
            value: ["高速公路", "一级公路"]
          },
          {
            label: "二级及以上",
            value: ["高速公路", "一级公路", "二级公路"]
          },
          {
            label: "三级及以上",
            value: ["高速公路", "一级公路", "二级公路", "三级公路"]
          },
          {
            label: "特大型桥梁工程",
            value: ["特大型桥梁工程"]
          },
          {
            label: "其他工程",
            value: ["其他工程"]
          }
        ],
        filterMultiple: false,
        filterMethod(value, row) {
          return value.indexOf(row.technologyGrade) != -1;
        }
      });
      mycolumns.push({
        title: "省份",
        key: "province",
        filters: this.getFilters,
        filterMultiple: true,
        filterMethod(value, row) {
          return value.indexOf(row.province) != -1;
        }
      });
      mycolumns.push({
        title: "开工日期",
        key: "beginDate",
        sortable: true
      });
      mycolumns.push({
        title: "交工日期",
        key: "handDate",
        sortable: true
      });
      mycolumns.push({
        title: "竣工日期",
        key: "endDate",
        sortable: true
      });
      mycolumns.push({
        title: "公里数(单位:m)",
        key: "kilometer"
      });
      mycolumns.push({
        title: "操作",
        slot: "action",
        width: 150,
        align: "center"
      });
      return mycolumns;
    }
  },
  data() {
    return {
      // showCheckbox: false,
      showIndex: false,
      pageIndex: 1,
      pageSize: 10,
      filters: [],
      downloading: false,
      totalData: [],
      isloading: false,
      total: 0,
      pageSizeOpts: [10, 20, 30, 40, 100, 200, 500],
      data6: [],
      keyWordsStr: "",
      keywordsArray: [],
      result: "暂无",
      form: {
        corpName: "",
        kilometerSmall: null,
        kilometerBig: null,
        endDate: [],
        keyWords: "",
        //technologyGrade: null,
        keyType: "and",
        constructStatus: null,
        handDate: "",
        priceSmall: null,
        priceBig: null
      },

      options: [
        { text: "条件为且关系", value: "and" },
        { text: "条件为或关系", value: "or" }
      ],
      foods: [
        { text: "高速公路", value: "高速公路" },
        { text: "一级及以上", value: "一级及以上" },
        { text: "二级及以上", value: "二级及以上" },
        { text: "三级及以上", value: "三级及以上" }
      ],
      constrStatus: [
        { text: "总包已建", value: "总包已建" },
        { text: "总包在建", value: "总包在建" }
      ],
      show: true
      //count: [0, 1, 2]
    };
  },
  watch: {
    keyWordsStr: {
      handler: function(newVal, oldVal) {
        if (newVal == "") {
          this.keywordsArray.splice(0, this.keywordsArray.length);
          return;
        }
        this.keywordsArray = newVal.split(" ");
      }
    },
    immediate: true
  },
  methods: {
    closeExpand() {
      for (var ind = 0; ind < this.data6.length; ind++) {
        this.data6[ind]._expanded = false;
      }
      this.data6.splice();
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => v[j]));
    },
    downloadSelected() {
      this.downloading = true;
      require.ensure([], () => {
        const { export_json_to_excel } = require("../excel/Export2Excel");
        const tHeader = [
          "公司名",
          "工程名称",
          "技术等级",
          "开工日期",
          "合同金额",
          "结算金额",
          "省份",
          "交工日期",
          "竣工日期",
          "主要业绩",
          "公里数(单位：m)"
        ]; //表头名称
        const filterVal = [
          "corpName",
          "projectName",
          "technologyGrade",
          "beginDate",
          "contractPrice",
          "settlementPrice",
          "province",
          "handDate",
          "endDate",
          "remark",
          "kilometer"
        ]; //与表格数据配合 可以是iview表格中的key的数组
        const list = this.data6; //表格数据，iview中表单数据也是这种格式！
        const data = this.formatJson(filterVal, list);
        export_json_to_excel(tHeader, data, "导出数据excel"); //列表excel  这个是导出表单的名称
        this.downloadLoading = false;
      });
    },
    handleAdd() {
      if (this.keywordsArray.length) {
        this.keywordsArray.push(
          this.keywordsArray[this.keywordsArray.length - 1] + 1
        );
      } else {
        this.keywords.push(0);
      }
    },
    handleClose2(event, name) {
      const index = this.keywordsArray.indexOf(name);
      this.keywordsArray.splice(index, 1);
      if (this.keyWordsStr.indexOf(" ") >= 0) {
        if (this.keyWordsStr.indexOf(name) === 0) {
          this.keyWordsStr = this.keyWordsStr.replace(name + " ", "");
        } else {
          this.keyWordsStr = this.keyWordsStr.replace(" " + name, "");
        }
      } else {
        this.keyWordsStr = this.keyWordsStr.replace(name, "");
      }
    },
    onPageSizeChange(value) {
      this.pageSize = value;
      var _start = (this.pageIndex - 1) * this.pageSize;
      var _end = this.pageIndex * this.pageSize;
      this.data6 = this.totalData.slice(_start, _end);
    },
    onChange(value) {
      this.pageIndex = value;
      var _start = (value - 1) * this.pageSize;
      var _end = value * this.pageSize;
      this.data6 = this.totalData.slice(_start, _end);
    },
    showInfo(index) {
      this.$Modal.info({
        title: "User Info",
        content: `Name：${this.data6[index].name}<br>Age：${this.data6[index].age}<br>Address：${this.data6[index].address}`
      });
    },
    onSubmit(evt) {
      evt.preventDefault();
      this.form.keyWords = this.keywordsArray.join(",");
      var query = this.Qs.stringify(this.form);
      this.isloading = true;
      this.Http.get("/spiderweb/queryItems?" + query)
        .then(res => {
          this.pageIndex = 1;
          this.pageSize = 10;
          console.log(res);
          if (res.data.data != null || res.data.data.length != 0) {
            this.totalData = res.data.data;
            this.data6 = this.totalData.slice(
              this.pageIndex - 1,
              this.pageSize
            );
            this.total = this.totalData.length;
          }
          this.isloading = false;
        })
        .then(() => {
          this.isloading = false;
        });
      console.log(res.data);
    },
    changeState(checked) {
      console.log("check: ", checked);
    },
    onReset(evt) {
      evt.preventDefault();
      this.form.corpName = "";
      this.form.keyType = "and";
      this.form.handDate = "";
      this.form.endDate = "";
      this.keyWordsStr = "";
      this.form.kilometerSmall = null;
      this.form.kilometerBig = null;
      //this.form.technologyGrade = null;
      this.form.constructStatus = null;
      this.form.priceBig = null;
      this.form.priceSmall = null;
      this.form.checked = [];
      this.show = false;
      this.keywordsArray = [];
      this.$nextTick(() => {
        this.show = true;
      });
    }
  }
};
</script>