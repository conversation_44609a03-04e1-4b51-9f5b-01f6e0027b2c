import Vue from 'vue'
import Router from 'vue-router'
import CorpSearch from '@/components/CorpSearch'
import Login from '@/components/Login'
import Content from '@/components/Content'
import SearchCompany from '@/components/SearchCompany'
import CorpSearchSingle from '@/components/CorpSearchSingle'
import CorpSearchMulti from '@/components/CorpSearchMulti'
Vue.use(Router)

export default new Router({
  //mode:'history',
  routes: [
    {
      path: '/',
      redirect: '/login',
    },
    {
      path: '/content',
      name: 'CompanySearch',
      component: Content,
      children: [
        {
          path: "corpsearch",
          name: "corpSearch",
          component: SearchCompany,
        },
        {
          path: "homepage",
          name: "homepage",
          component: CorpSearch,
          redirect: '/content/homepage/CorpSearchMulti' ,
          children: [
            {
              path: "corpsearchmulti",
              name: "corpsearchmulti",
              component: CorpSearchMulti,
            },
            {
              path: "corpsearchsingle",
              name: "corpsearchsingle",
              component: CorpSearchSingle,
            }
          ]
        }
      ],
    },
    {
      path: '/login',
      component: Login
    }

  ]
})
